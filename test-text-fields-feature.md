# ميزة تحويل حقول الولايات والبلديات إلى حقول نصية

## الوصف
تم إضافة ميزة جديدة في تبويبة "إعدادات النموذج" تسمح بتحويل حقول الولايات والبلديات من قوائم منسدلة إلى حقول نصية قابلة للكتابة.

## الملفات المعدلة

### 1. includes/class-rid-cod-customizer.php
- إضافة إعداد جديد: `rid_cod_enable_text_fields`
- إضافة حقل في تبويبة "إعدادات النموذج" بعنوان "تحويل الولايات والبلديات إلى حقول نصية"

### 2. includes/class-rid-cod-form.php
- إضافة متغير `$enable_text_fields` لقراءة الإعداد
- تعديل منطق عرض حقول الولايات والبلديات لدعم الحقول النصية
- تمرير الإعداد إلى JavaScript

### 3. assets/js/rid-cod.js
- إضافة دالة `shouldUseStateAsTextInput()` للتحقق من حالة الولايات
- تعديل دالة `shouldUseCityAsTextInput()` لدعم الإعداد الجديد
- إضافة دالة `convertStateSelectToTextInput()` لتحويل حقل الولايات
- إضافة دالة `ensureStateIsSelectDropdown()` لإعادة تحويل حقل الولايات
- تعديل دالة التهيئة لدعم الحقول النصية
- تعديل منطق تغيير الدولة لدعم الحقول النصية

## كيفية الاستخدام

1. اذهب إلى لوحة تحكم WordPress
2. انتقل إلى "إعدادات الدفع عند الاستلام"
3. اختر تبويبة "إعدادات النموذج"
4. فعّل خيار "تحويل الولايات والبلديات إلى حقول نصية"
5. احفظ الإعدادات

## السلوك المتوقع

### عند تفعيل الميزة:
- حقل الولايات يصبح حقل نص قابل للكتابة
- حقل البلديات يصبح حقل نص قابل للكتابة
- يتم استخدام أسعار الشحن العامة بدلاً من أسعار الولايات المحددة
- العملاء يمكنهم كتابة أي ولاية أو بلدية

### عند إلغاء تفعيل الميزة:
- حقل الولايات يعود إلى قائمة منسدلة
- حقل البلديات يعود إلى قائمة منسدلة
- يتم استخدام أسعار الشحن المحددة لكل ولاية
- العملاء يختارون من القوائم المحددة مسبقاً

## المزايا
- مرونة أكبر للعملاء في إدخال المواقع
- سهولة الاستخدام للمناطق غير المدرجة في القوائم
- تبسيط عملية الطلب
- دعم المناطق الجديدة دون الحاجة لتحديث القوائم

## التحديثات الإضافية

### إصلاح مشكلة التوجيه إلى صفحة الشكر
- **المشكلة:** عند تفعيل الحقول النصية، لم يكن يتم التوجيه إلى صفحة الشكر
- **الحل:** تم إصلاح منطق استخراج اسم الولاية في JavaScript للتعامل مع الحقول النصية
- **الملفات المعدلة:** `assets/js/rid-cod.js`

### إضافة خيار تعطيل ملخص الطلب المخصص
- **الميزة الجديدة:** إعداد جديد لتعطيل ملخص الطلب المخصص في صفحة الشكر
- **الموقع:** تبويبة "إعدادات النموذج" > "تعطيل ملخص الطلب المخصص في صفحة الشكر"
- **الفائدة:** عرض صفحة الشكر الافتراضية من ووكومرس بدون تخصيصات

## ملاحظات تقنية
- الميزة تعمل مع جميع الدول المدعومة
- عند استخدام الحقول النصية، يتم تطبيق أسعار الشحن العامة
- الميزة متوافقة مع جميع الإعدادات الأخرى للمكون الإضافي
- لا تؤثر على البيانات المحفوظة مسبقاً
- تم إصلاح مشكلة التوجيه إلى صفحة الشكر عند استخدام الحقول النصية
- يمكن تعطيل ملخص الطلب المخصص لعرض صفحة الشكر الافتراضية
